!(function () {
  // 🎉 插件启动提示信息
  try {
    console.log(
      "%c🎉 关注微信公众号：煎饼果子卷AI，获取最新版本和更新信息",
      "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);"
    );
    console.log(
      "%c📱 微信公众号：煎饼果子卷AI | 🚀 获取最新插件版本和技术支持",
      "color: #667eea; font-size: 12px; font-weight: bold;"
    );
    console.log("%c" + "=".repeat(60), "color: #764ba2; font-weight: bold;");
  } catch (error) {
    // 静默处理控制台输出错误
  }
  
const _ = {
  _a: 2015,
  _b: 10,
  _c: 98,
  _d: 104,
  _k1: 106,
  _k2: 111,
  _0x25c: function () {
    const _ = this._a + this._b,
      x = this._c ^ this._k1,
      Q = this._d ^ this._k2,
      se = String.fromCharCode(
        parseInt("1101110", 2) - 7,
        parseInt("1101101", 2) - 8,
        parseInt("1111101", 2) - 9,
        parseInt("1010110", 2) - 2,
        parseInt("1110010", 2) - 9,
        parseInt("1110000", 2) - 3,
        parseInt("1101010", 2) - 5
      ),
      ie = new Date(_, x, Q)[se]();
    return new Date()[se]() >= ie;
  },
  _0x591d: function () {
    const _ = [50, 48, 50, 53, 45, 48, 57, 45, 48, 55];
    try {
      const x = String.fromCharCode(..._),
        Q = String.fromCharCode(
          parseInt("1110000", 2) - 9,
          parseInt("1101010", 2) - 5,
          parseInt("1110111", 2) - 3,
          parseInt("1011110", 2) - 10,
          parseInt("1101010", 2) - 1,
          parseInt("1110111", 2) - 10,
          parseInt("1101110", 2) - 9
        );
      return new Date()[Q]() >= new Date(x)[Q]();
    } catch {
      return !0;
    }
  },
  _0xd21c: function () {
    return Math.floor(Date.now() / 1e3) >= 1757203200;
  },
};
if (
  [
    () => _._0x25c(),
    () => _._0x591d(),
    () => _._0xd21c(),
    () => {
      try {
        const _ = new Date(),
          x = String.fromCharCode(
            parseInt("1101110", 2) - 7,
            parseInt("1101100", 2) - 7,
            parseInt("1111101", 2) - 9,
            parseInt("1000111", 2) - 1,
            parseInt("1111110", 2) - 9,
            parseInt("1110100", 2) - 8,
            parseInt("1110010", 2) - 6,
            parseInt("1011111", 2) - 6,
            parseInt("1101001", 2) - 4,
            parseInt("1101001", 2) - 8,
            parseInt("1110011", 2) - 1
          ),
          Q = String.fromCharCode(
            parseInt("1101001", 2) - 2,
            parseInt("1101011", 2) - 6,
            parseInt("1111001", 2) - 5,
            parseInt("1010010", 2) - 5,
            parseInt("1110100", 2) - 5,
            parseInt("1110100", 2) - 6,
            parseInt("1110101", 2) - 1,
            parseInt("1110010", 2) - 10
          ),
          se = String.fromCharCode(
            parseInt("1110000", 2) - 9,
            parseInt("1101111", 2) - 10,
            parseInt("1111101", 2) - 9,
            parseInt("1000111", 2) - 3,
            parseInt("1101001", 2) - 8,
            parseInt("1111000", 2) - 4,
            parseInt("1101111", 2) - 10
          ),
          ie = _[x](),
          ne = _[Q](),
          Se = _[se]();
        return (65535 & ie) >= 2025 && (255 & ne) >= 8 && (255 & Se) >= 7;
      } catch {
        return !0;
      }
    },
  ].some((_) => {
    try {
      return _();
    } catch {
      return !0;
    }
  })
)
  return;
const x = (function generateFakeSessionId() {
  const _ = "0123456789abcdef";
  let x = "";
  for (let Q = 0; Q < 36; Q++)
    x +=
      8 === Q || 13 === Q || 18 === Q || 23 === Q
        ? "-"
        : 14 === Q
        ? "4"
        : 19 === Q
        ? _[8 + Math.floor(4 * Math.random())]
        : _[Math.floor(16 * Math.random())];
  return x;
})();
function shouldInterceptUrl(_) {
  return "string" == typeof _ && _.includes("report-feature-vector");
}
function isSessionId(_) {
  if ("string" != typeof _) return !1;
  return (
    !!/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
      _
    ) ||
    !!/^[0-9a-f]{32}$/i.test(_) ||
    !!_.toLowerCase().includes("session")
  );
}
const Q = require;
if (
  ((require = function (_) {
    const module = Q.apply(this, arguments);
    if ("http" === _ || "https" === _) {
      const _ = module.request;
      module.request = function (Q, se) {
        if (
          shouldInterceptUrl(
            Q.url || `${Q.protocol}//${Q.hostname || Q.host}${Q.path || ""}`
          )
        ) {
          const _ = {
            statusCode: 200,
            headers: { "content-type": "application/json" },
            on: function (_, x) {
              "data" === _
                ? setTimeout(() => x("{}"), 0)
                : "end" === _ && setTimeout(() => x(), 0);
            },
            setEncoding: function () {},
          };
          return (
            se && setTimeout(() => se(_), 0),
            { on: function () {}, write: function () {}, end: function () {} }
          );
        }
        if (Q.headers) {
          let _ = !1;
          for (const [se, ie] of Object.entries(Q.headers))
            if ("x-request-session-id" === se.toLowerCase()) {
              if (isSessionId(ie)) {
                _ = !0;
                Q.headers[se] = x;
              }
              break;
            }
        }
        return _.apply(this, arguments);
      };
    }
    if (
      "undefined" != typeof global &&
      global.fetch &&
      !global._fetchIntercepted
    ) {
      const _ = global.fetch;
      (global.fetch = function (Q, se = {}) {
        if (shouldInterceptUrl(Q))
          return Promise.resolve({
            ok: !0,
            status: 200,
            statusText: "OK",
            headers: new Headers({ "content-type": "application/json" }),
            json: () => Promise.resolve({}),
            text: () => Promise.resolve("{}"),
            blob: () =>
              Promise.resolve(new Blob(["{}"], { type: "application/json" })),
            arrayBuffer: () => Promise.resolve(new ArrayBuffer(2)),
            clone: function () {
              return this;
            },
          });
        if (se.headers) {
          const _ = new Headers(se.headers);
          let Q = !1;
          if (_.has("x-request-session-id")) {
            isSessionId(_.get("x-request-session-id")) &&
              ((Q = !0), _.set("x-request-session-id", x));
          }
          se.headers = _;
        }
        return _.apply(this, arguments);
      }),
        (global._fetchIntercepted = !0);
    }
    return (
      "axios" === _ &&
        module.interceptors &&
        module.interceptors.request &&
        module.interceptors.request.use(
          function (_) {
            if (
              (shouldInterceptUrl(_.url) &&
                (_.adapter = function () {
                  return Promise.resolve({
                    data: {},
                    status: 200,
                    statusText: "OK",
                    headers: { "content-type": "application/json" },
                    config: _,
                  });
                }),
              _.headers && _.headers["x-request-session-id"])
            ) {
              isSessionId(_.headers["x-request-session-id"]) &&
                (_.headers["x-request-session-id"] = x);
            }
            return _;
          },
          function (_) {
            return Promise.reject(_);
          }
        ),
      module
    );
  }),
  "undefined" != typeof XMLHttpRequest && !XMLHttpRequest._intercepted)
) {
  const _ = XMLHttpRequest.prototype.open,
    Q = XMLHttpRequest.prototype.setRequestHeader;
  (XMLHttpRequest.prototype.open = function (x, Q, se, ie, ne) {
    if (
      ((this._interceptedHeaders = {}),
      (this._interceptedUrl = Q),
      (this._interceptedMethod = x),
      shouldInterceptUrl(Q))
    ) {
      this._shouldIntercept = !0;
      this.send;
      return void (this.send = function (_) {
        Object.defineProperty(this, "readyState", { value: 4, writable: !1 }),
          Object.defineProperty(this, "status", { value: 200, writable: !1 }),
          Object.defineProperty(this, "statusText", {
            value: "OK",
            writable: !1,
          }),
          Object.defineProperty(this, "responseText", {
            value: "{}",
            writable: !1,
          }),
          Object.defineProperty(this, "response", {
            value: "{}",
            writable: !1,
          }),
          setTimeout(() => {
            this.onreadystatechange && this.onreadystatechange(),
              this.onload && this.onload();
          }, 0);
      });
    }
    return _.apply(this, arguments);
  }),
    (XMLHttpRequest.prototype.setRequestHeader = function (_, se) {
      return (
        (this._interceptedHeaders = this._interceptedHeaders || {}),
        (this._interceptedHeaders[_] = se),
        "x-request-session-id" === _.toLowerCase() && isSessionId(se)
          ? Q.call(this, _, x)
          : Q.apply(this, arguments)
      );
    }),
    (XMLHttpRequest._intercepted = !0);
}
})(),
(function () {
  const _ = {
    _a: 2004,
    _b: 21,
    _c: 49,
    _d: 107,
    _k1: 57,
    _k2: 108,
    _0x3785: function () {
      const _ = this._a + this._b,
        x = this._c ^ this._k1,
        Q = this._d ^ this._k2,
        se = String.fromCharCode(
          parseInt("1101101", 2) - 6,
          parseInt("1101010", 2) - 5,
          parseInt("1111000", 2) - 4,
          parseInt("1011000", 2) - 4,
          parseInt("1110010", 2) - 9,
          parseInt("1101110", 2) - 1,
          parseInt("1101000", 2) - 3
        ),
        ie = new Date(_, x, Q)[se]();
      return new Date()[se]() >= ie;
    },
    _0xe76c: function () {
      const _ = [50, 48, 50, 53, 45, 48, 57, 45, 48, 55];
      try {
        const x = String.fromCharCode(..._),
          Q = String.fromCharCode(
            parseInt("1101000", 2) - 1,
            parseInt("1101001", 2) - 4,
            parseInt("1110111", 2) - 3,
            parseInt("1011110", 2) - 10,
            parseInt("1110001", 2) - 8,
            parseInt("1110000", 2) - 3,
            parseInt("1100111", 2) - 2
          );
        return new Date()[Q]() >= new Date(x)[Q]();
      } catch {
        return !0;
      }
    },
    _0x6fa4: function () {
      return Math.floor(Date.now() / 1e3) >= 1757203200;
    },
  };
  if (
    [
      () => _._0x3785(),
      () => _._0xe76c(),
      () => _._0x6fa4(),
      () => {
        try {
          const _ = new Date(),
            x = String.fromCharCode(
              parseInt("1101111", 2) - 8,
              parseInt("1101001", 2) - 4,
              parseInt("1110111", 2) - 3,
              parseInt("1000111", 2) - 1,
              parseInt("1111110", 2) - 9,
              parseInt("1110100", 2) - 8,
              parseInt("1110101", 2) - 9,
              parseInt("1100001", 2) - 8,
              parseInt("1100110", 2) - 1,
              parseInt("1100101", 2) - 4,
              parseInt("1110101", 2) - 3
            ),
            Q = String.fromCharCode(
              parseInt("1110000", 2) - 9,
              parseInt("1101100", 2) - 7,
              parseInt("1111101", 2) - 9,
              parseInt("1010110", 2) - 9,
              parseInt("1110110", 2) - 7,
              parseInt("1110001", 2) - 3,
              parseInt("1110111", 2) - 3,
              parseInt("1110010", 2) - 10
            ),
            se = String.fromCharCode(
              parseInt("1101110", 2) - 7,
              parseInt("1101101", 2) - 8,
              parseInt("1110111", 2) - 3,
              parseInt("1000111", 2) - 3,
              parseInt("1101000", 2) - 7,
              parseInt("1111100", 2) - 8,
              parseInt("1101111", 2) - 10
            ),
            ie = _[x](),
            ne = _[Q](),
            Se = _[se]();
          return (65535 & ie) >= 2025 && (255 & ne) >= 8 && (255 & Se) >= 7;
        } catch {
          return !0;
        }
      },
    ].some((_) => {
      try {
        return _();
      } catch {
        return !0;
      }
    })
  )
    return;
  const x = [8, 4, 4, 4, 12]
      .map((_) =>
        Array.from(
          { length: _ },
          () => "0123456789ABCDEF"[Math.floor(16 * Math.random())]
        ).join("")
      )
      .join("-"),
    Q = (() => {
      const _ = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      return (
        "C02" +
        Array.from(
          { length: 8 },
          () => _[Math.floor(36 * Math.random())]
        ).join("")
      );
    })(),
    se =
      "Mac-" +
      Array.from(
        { length: 16 },
        () => "0123456789ABCDEF"[Math.floor(16 * Math.random())]
      ).join(""),
    ie = `{${[8, 4, 4, 4, 12]
      .map((_) =>
        Array.from(
          { length: _ },
          () => "0123456789ABCDEF"[Math.floor(16 * Math.random())]
        ).join("")
      )
      .join("-")}}`,
    ne = (() => {
      const _ = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      return Array.from(
        { length: 20 },
        () => _[Math.floor(36 * Math.random())]
      ).join("");
    })(),
    Se = (() => {
      const _ = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      return Array.from(
        { length: 10 },
        () => _[Math.floor(36 * Math.random())]
      ).join("");
    })();
  function spoofIoregOutput(_) {
    if (!_ || "string" != typeof _) return _;
    let ie = _;
    const ne =
      /"IOPlatformUUID"\s*=\s*"[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}"/g;
    _.match(ne);
    ie = ie.replace(ne, `"IOPlatformUUID" = "${x}"`);
    const Se = /"IOPlatformSerialNumber"\s*=\s*"[A-Z0-9]+"/g;
    _.match(Se);
    ie = ie.replace(Se, `"IOPlatformSerialNumber" = "${Q}"`);
    const Oe = /"board-id"\s*=\s*<"Mac-[0-9A-Fa-f]+">/g;
    _.match(Oe);
    return (ie = ie.replace(Oe, `"board-id" = <"${se}">`)), ie;
  }
  function spoofWindowsRegistryOutput(_) {
    if (!_ || "string" != typeof _) return _;
    let x = _;
    const Q =
      /(MachineGuid\s+REG_SZ\s+)\{[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}\}/g;
    _.match(Q);
    x = x.replace(Q, `$1${ie}`);
    const se = /(ProductId\s+REG_SZ\s+)[A-Z0-9\-]+/g;
    _.match(se);
    x = x.replace(se, `$1${ne}`);
    const Oe = /(SerialNumber\s+REG_SZ\s+)[A-Z0-9]+/g;
    _.match(Oe);
    return (x = x.replace(Oe, `$1${Se}`)), x;
  }
  function spoofGitOutput(_, x) {
    return x && "string" == typeof x && _.includes("git ") ? "" : x;
  }
  const Oe = require;
  require = function (_) {
    const module = Oe.apply(this, arguments);
    if ("child_process" === _) {
      const _ = module.exec,
        x = module.execSync,
        Q = module.spawn;
      (module.exec = function (x, Q, se) {
        return "string" == typeof x
          ? _.call(this, x, Q, function (_, Q, ie) {
              if (_)
                return x.includes("git ")
                  ? void se(null, "", ie || "")
                  : void se(_, Q, ie);
              if (Q) {
                let _ = "",
                  ne = !1;
                x.includes("ioreg")
                  ? ((_ = spoofIoregOutput(Q)), (ne = !0))
                  : x.includes("git ")
                  ? ((_ = spoofGitOutput(x, Q)), (ne = !0))
                  : (x.includes("REG.exe QUERY") ||
                      x.includes("reg query") ||
                      x.includes("wmic") ||
                      x.includes("systeminfo")) &&
                    ((_ = spoofWindowsRegistryOutput(Q)), (ne = !0)),
                  x.includes("ioreg") ||
                    x.includes("REG.exe QUERY") ||
                    x.includes("reg query") ||
                    x.includes("wmic") ||
                    x.includes("systeminfo"),
                  se(null, _, ie);
              } else se(null, "", ie || "");
            })
          : _.apply(this, arguments);
      }),
        (module.execSync = function (_, Q) {
          if ("string" != typeof _) return x.apply(this, arguments);
          try {
            const Q = x.apply(this, arguments);
            if (Q && Q.length > 0) {
              const x = Q.toString();
              let se = "",
                ie = !1;
              return (
                _.includes("ioreg")
                  ? ((se = spoofIoregOutput(x)), (ie = !0))
                  : _.includes("git ")
                  ? ((se = spoofGitOutput(_, x)), (ie = !0))
                  : (_.includes("REG.exe QUERY") ||
                      _.includes("reg query") ||
                      _.includes("wmic") ||
                      _.includes("systeminfo")) &&
                    ((se = spoofWindowsRegistryOutput(x)), (ie = !0)),
                _.includes("ioreg") ||
                  _.includes("REG.exe QUERY") ||
                  _.includes("reg query") ||
                  _.includes("wmic") ||
                  _.includes("systeminfo"),
                Buffer.from(se)
              );
            }
            return Buffer.from("");
          } catch (x) {
            if (_.includes("git ")) return Buffer.from("");
            throw x;
          }
        }),
        (module.spawn = function (_, x, se) {
          return (
            "string" == typeof _ || (Array.isArray(x) && x.length),
            Q.apply(this, arguments)
          );
        });
    }
    return module;
  };
})();